{"name": "@types/crypto-js", "version": "4.1.1", "description": "TypeScript definitions for crypto-js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/crypto-js", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/misak113", "githubUsername": "misak113"}, {"name": "<PERSON>", "url": "https://github.com/maximlysenko", "githubUsername": "maxi<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/mymindstorm", "githubUsername": "mymindstorm"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/SevenOutman", "githubUsername": "SevenOutman"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/crypto-js"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "e944cfde55e345542aa08e561d872f5a9d05027757a5a9ee196c18da1f945f58", "typeScriptVersion": "3.8"}