{"Type": "Live2D Expression", "Parameters": [{"Id": "ParamBrowLY", "Value": -1, "Blend": "Add"}, {"Id": "ParamBrowRY", "Value": -1, "Blend": "Add"}, {"Id": "ParamBrowLX", "Value": -1, "Blend": "Add"}, {"Id": "ParamBrowRX", "Value": -1, "Blend": "Add"}, {"Id": "ParamBrowRAngle", "Value": -1, "Blend": "Add"}, {"Id": "ParamBrowLForm", "Value": -1, "Blend": "Add"}, {"Id": "ParamBrowRForm", "Value": -1, "Blend": "Add"}, {"Id": "ParamMouthForm", "Value": -2, "Blend": "Add"}, {"Id": "ParamMouthOpenY", "Value": 1, "Blend": "Add"}, {"Id": "ParamEyeForm", "Value": -1, "Blend": "Add"}]}