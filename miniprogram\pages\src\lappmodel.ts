/**
 * Copyright(c) Live2D Inc. All rights reserved.
 *
 * Use of this source code is governed by the Live2D Open Software license
 * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.
 */

import { CubismDefaultParameterId } from '../src1/cubismdefaultparameterid';
import { CubismModelSettingJson } from '../src1/cubismmodelsettingjson';
import {
    BreathParameterData,
    CubismBreath
} from '../src1/effect/cubismbreath';
import { CubismEyeBlink } from '../src1/effect/cubismeyeblink';
import { ICubismModelSetting } from '../src1/icubismmodelsetting';
import { CubismIdHandle } from '../src1/id/cubismid';
import { CubismFramework } from '../src1/live2dcubismframework';
import { CubismMatrix44 } from '../src1/math/cubismmatrix44';
import { CubismUserModel } from '../src1/model/cubismusermodel';
import {
    ACubismMotion,
    FinishedMotionCallback
} from '../src1/motion/acubismmotion';
import { CubismMotion } from '../src1/motion/cubismmotion';
import {
    CubismMotionQueueEntryHandle,
    InvalidMotionQueueEntryHandleValue
} from '../src1/motion/cubismmotionqueuemanager';
import { csmMap } from '../src1/type/csmmap';
import { csmRect } from '../src1/type/csmrectf';
import { csmString } from '../src1/type/csmstring';
import { csmVector } from '../src1/type/csmvector';
import { CubismLogInfo } from '../src1/utils/cubismdebug';

import * as LAppDefine from './lappdefine';
import { canvas, frameBuffer, gl, LAppDelegate } from './lappdelegate';
import { LAppPal } from './lapppal';
import { TextureInfo } from './lapptexturemanager';
import { LAppWavFileHandler } from './lappwavfilehandler';

/**
 * ユーザーが実際に使用するモデルの実装クラス<br>
 * モデル生成、機能コンポーネント生成、更新処理とレンダリングの呼び出しを行う。
 */
export class LAppModel extends CubismUserModel {
    /**
     * model3.jsonが置かれたディレクトリとファイルパスからモデルを生成する
     * @param dir
     * @param fileName
     */
    public loadAssets(dir: string, fileName: string): void {
        this._modelHomeDir = dir;
        
        try {
            // 使用本地文件系统直接读取文件
            const fs = wx.getFileSystemManager();
            const filePath = `${this._modelHomeDir}${fileName}`;
            
            // 读取本地文件
            const arrayBuffer = fs.readFileSync(filePath);
            const setting: ICubismModelSetting = new CubismModelSettingJson(
                arrayBuffer,
                arrayBuffer.byteLength
            );
            this.setupModel(setting);
        } catch (error) {
            console.error('加载本地模型文件失败:', error);
            console.error('文件路径:', `${this._modelHomeDir}${fileName}`);
        }
    }

    /**
     * model3.jsonからモデルを生成する。
     * model3.jsonの記述に従ってモデル生成、モーション、物理演算などのコンポーネント生成を行う。
     *
     * @param setting ICubismModelSettingのインスタンス
     */
    private setupModel(setting: ICubismModelSetting): void {
        this._updating = true;
        this._initialized = false;
        this._modelSetting = setting;

        try {
            // 同步加载所有模型文件
            this.loadModelSync();
            this.loadExpressionsSync();
            this.loadPhysicsSync();
            this.loadPoseSync();
            this.loadUserDataSync();
            this.setupEyeBlink();
            this.setupBreath();
            this.setupEyeBlinkIds();
            this.setupLipSyncIds();
            this.setupLayout();
            this.loadMotionsSync();
            
            // 完成初始化
            this._updating = false;
            this._initialized = true;
            this.createRenderer();
            this.setupTextures();
            this.getRenderer().startUp(gl);
            
            console.log('模型加载完成');
        } catch (error) {
            console.error('模型加载失败:', error);
        }
    }

    /**
     * 同步加载模型文件
     */
    private loadModelSync(): void {
        if (this._modelSetting.getModelFileName() != '') {
            const modelFileName = this._modelSetting.getModelFileName();
            const fs = wx.getFileSystemManager();
            const filePath = `${this._modelHomeDir}${modelFileName}`;
            const arrayBuffer = fs.readFileSync(filePath);
            this.loadModel(arrayBuffer);
        } else {
            LAppPal.printMessage('Model data does not exist.');
        }
    }

    /**
     * 同步加载表情文件
     */
    private loadExpressionsSync(): void {
        if (this._modelSetting.getExpressionCount() > 0) {
            const count: number = this._modelSetting.getExpressionCount();
            const fs = wx.getFileSystemManager();

            for (let i = 0; i < count; i++) {
                const expressionName = this._modelSetting.getExpressionName(i);
                const expressionFileName = this._modelSetting.getExpressionFileName(i);
                const filePath = `${this._modelHomeDir}${expressionFileName}`;
                const arrayBuffer = fs.readFileSync(filePath);
                const motion: ACubismMotion = this.loadExpression(
                    arrayBuffer,
                    arrayBuffer.byteLength,
                    expressionName
                );

                if (this._expressions.getValue(expressionName) != null) {
                    ACubismMotion.delete(this._expressions.getValue(expressionName));
                    this._expressions.setValue(expressionName, null);
                }

                this._expressions.setValue(expressionName, motion);
            }
        }
    }

    /**
     * 同步加载物理文件
     */
    private loadPhysicsSync(): void {
        if (this._modelSetting.getPhysicsFileName() != '') {
            const physicsFileName = this._modelSetting.getPhysicsFileName();
            const fs = wx.getFileSystemManager();
            const filePath = `${this._modelHomeDir}${physicsFileName}`;
            const arrayBuffer = fs.readFileSync(filePath);
            this.loadPhysics(arrayBuffer, arrayBuffer.byteLength);
        }
    }

    /**
     * 同步加载Pose文件
     */
    private loadPoseSync(): void {
        if (this._modelSetting.getPoseFileName() != '') {
            const poseFileName = this._modelSetting.getPoseFileName();
            const fs = wx.getFileSystemManager();
            const filePath = `${this._modelHomeDir}${poseFileName}`;
            const arrayBuffer = fs.readFileSync(filePath);
            this.loadPose(arrayBuffer, arrayBuffer.byteLength);
        }
    }

    /**
     * 同步加载UserData文件
     */
    private loadUserDataSync(): void {
        if (this._modelSetting.getUserDataFile() != '') {
            const userDataFile = this._modelSetting.getUserDataFile();
            const fs = wx.getFileSystemManager();
            const filePath = `${this._modelHomeDir}${userDataFile}`;
            const arrayBuffer = fs.readFileSync(filePath);
            this.loadUserData(arrayBuffer, arrayBuffer.byteLength);
        }
    }

    /**
     * 同步加载动作文件
     */
    private loadMotionsSync(): void {
        const fs = wx.getFileSystemManager();
        this._allMotionCount = 0;
        this._motionCount = 0;

        // 计算总动作数量
        for (let i = 0; i < this._modelSetting.getMotionGroupCount(); i++) {
            const group = this._modelSetting.getMotionGroupName(i);
            this._allMotionCount += this._modelSetting.getMotionCount(group);
        }

        // 加载所有动作
        for (let i = 0; i < this._modelSetting.getMotionGroupCount(); i++) {
            const group = this._modelSetting.getMotionGroupName(i);
            this.preLoadMotionGroup(group);
        }
    }

    /**
     * 预加载动作组
     */
    private preLoadMotionGroup(group: string): void {
        const fs = wx.getFileSystemManager();
        for (let i = 0; i < this._modelSetting.getMotionCount(group); i++) {
            const motionFileName = this._modelSetting.getMotionFileName(group, i);
            const name = `${group}_${i}`;
            
            try {
                const filePath = `${this._modelHomeDir}${motionFileName}`;
                const arrayBuffer = fs.readFileSync(filePath);
                const tmpMotion: CubismMotion = this.loadMotion(
                    arrayBuffer,
                    arrayBuffer.byteLength,
                    name
                );

                let fadeTime = this._modelSetting.getMotionFadeInTimeValue(group, i);
                if (fadeTime >= 0.0) {
                    tmpMotion.setFadeInTime(fadeTime);
                }

                fadeTime = this._modelSetting.getMotionFadeOutTimeValue(group, i);
                if (fadeTime >= 0.0) {
                    tmpMotion.setFadeOutTime(fadeTime);
                }
                tmpMotion.setEffectIds(this._eyeBlinkIds, this._lipSyncIds);

                if (this._motions.getValue(name) != null) {
                    ACubismMotion.delete(this._motions.getValue(name));
                }

                this._motions.setValue(name, tmpMotion);
                this._motionCount++;
            } catch (error) {
                console.error('加载动作文件失败:', error);
                console.error('文件路径:', `${this._modelHomeDir}${motionFileName}`);
            }
        }
    }

    /**
     * 设置眨眼功能
     */
    private setupEyeBlink(): void {
        // 眨眼功能设置
        if (this._modelSetting.getEyeBlinkParameterCount() > 0) {
            this._eyeBlink = CubismEyeBlink.create(this._modelSetting);
        }
    }

    /**
     * 设置呼吸功能
     */
    private setupBreath(): void {
        this._breath = CubismBreath.create();
        const breathParameters: csmVector<BreathParameterData> = new csmVector();
        breathParameters.pushBack(
            new BreathParameterData(this._idParamAngleX, 0.0, 15.0, 6.5345, 0.5)
        );
        breathParameters.pushBack(
            new BreathParameterData(this._idParamAngleY, 0.0, 8.0, 3.5345, 0.5)
        );
        breathParameters.pushBack(
            new BreathParameterData(
                this._idParamAngleZ,
                0.0,
                10.0,
                5.5345,
                0.5
            )
        );
        breathParameters.pushBack(
            new BreathParameterData(this._idParamBodyAngleX, 0.0, 4.0, 15.5345, 0.5)
        );
        breathParameters.pushBack(
            new BreathParameterData(
                CubismFramework.getIdManager().getId(
                    CubismDefaultParameterId.ParamBreath
                ),
                0.0,
                0.5,
                3.2345,
                0.5
            )
        );

        this._breath.setParameters(breathParameters);
    }

    /**
     * 设置眨眼ID
     */
    private setupEyeBlinkIds(): void {
        const eyeBlinkIdCount: number = this._modelSetting.getEyeBlinkParameterCount();

        for (let i = 0; i < eyeBlinkIdCount; ++i) {
            this._eyeBlinkIds.pushBack(this._modelSetting.getEyeBlinkParameterId(i));
        }
    }

    /**
     * 设置口型同步ID
     */
    private setupLipSyncIds(): void {
        const lipSyncIdCount = this._modelSetting.getLipSyncParameterCount();

        for (let i = 0; i < lipSyncIdCount; ++i) {
            this._lipSyncIds.pushBack(this._modelSetting.getLipSyncParameterId(i));
        }
    }

    /**
     * 设置布局
     */
    private setupLayout(): void {
        const layout: csmMap<string, number> = new csmMap<string, number>();
        this._modelSetting.getLayoutMap(layout);
        this._modelMatrix.setupFromLayout(layout);
    }

    /**
     * 引数で指定したモーションの再生を開始する
     * @param group モーショングループ名
     * @param no グループ内の番号
     * @param priority 優先度
     * @param onFinishedMotionHandler モーション再生終了時に呼び出されるコールバック関数
     * @return 開始したモーションの識別番号を返す。個別のモーションが終了したか否かを判定するisFinished()の引数で使用する。開始できない時は[-1]
     */
    public startMotion(
        group: string,
        no: number,
        priority: number,
        onFinishedMotionHandler?: FinishedMotionCallback
    ): CubismMotionQueueEntryHandle {
        // 检查模型是否已经加载完成
        if (!this._modelSetting) {
            console.error('模型配置未加载完成，无法播放动作');
            return InvalidMotionQueueEntryHandleValue;
        }

        if (priority == LAppDefine.PriorityForce) {
            this._motionManager.setReservePriority(priority);
        } else if (!this._motionManager.reserveMotion(priority)) {
            if (this._debugMode) {
                LAppPal.printMessage("[APP]can't start motion.");
            }
            return InvalidMotionQueueEntryHandleValue;
        }

        const motionFileName = this._modelSetting.getMotionFileName(group, no);

        // ex) idle_0
        const name = `${group}_${no}`;
        let motion: CubismMotion = this._motions.getValue(name) as CubismMotion;
        let autoDelete = false;

        if (motion == null) {
            // 如果动作没有预加载，尝试即时加载
            try {
                const fs = wx.getFileSystemManager();
                const filePath = `${this._modelHomeDir}${motionFileName}`;
                const arrayBuffer = fs.readFileSync(filePath);
                motion = this.loadMotion(
                    arrayBuffer,
                    arrayBuffer.byteLength,
                    null,
                    onFinishedMotionHandler
                );
                let fadeTime: number = this._modelSetting.getMotionFadeInTimeValue(
                    group,
                    no
                );

                if (fadeTime >= 0.0) {
                    motion.setFadeInTime(fadeTime);
                }

                fadeTime = this._modelSetting.getMotionFadeOutTimeValue(group, no);
                if (fadeTime >= 0.0) {
                    motion.setFadeOutTime(fadeTime);
                }

                motion.setEffectIds(this._eyeBlinkIds, this._lipSyncIds);
                autoDelete = true; // 終了時にメモリから削除
            } catch (error) {
                console.error('加载动作文件失败:', error);
                console.error('文件路径:', `${this._modelHomeDir}${motionFileName}`);
                return InvalidMotionQueueEntryHandleValue;
            }
        } else {
            motion.setFinishedMotionHandler(onFinishedMotionHandler);
        }

        //voice
        const voice = this._modelSetting.getMotionSoundFileName(group, no);
        if (voice.localeCompare('') != 0) {
            let path = voice;
            path = this._modelHomeDir + path;
            this._wavFileHandler.start(path);
        }

        if (this._debugMode) {
            LAppPal.printMessage(`[APP]start motion: [${group}_${no}`);
        }
        return this._motionManager.startMotionPriority(
            motion,
            autoDelete,
            priority
        );
    }

    // 添加其他必要的属性和方法
    public _modelHomeDir: string = '';
    public _modelSetting: ICubismModelSetting = null;
    public _userTimeSeconds: number = 0.0;
    public _eyeBlinkIds: csmVector<CubismIdHandle> = new csmVector<CubismIdHandle>();
    public _lipSyncIds: csmVector<CubismIdHandle> = new csmVector<CubismIdHandle>();
    public _motions: csmMap<string, ACubismMotion> = new csmMap<string, ACubismMotion>();
    public _expressions: csmMap<string, ACubismMotion> = new csmMap<string, ACubismMotion>();
    public _allMotionCount: number = 0;
    public _motionCount: number = 0;
    public _expressionCount: number = 0;
    public _textureManager: any = null;
    public _wavFileHandler: LAppWavFileHandler = new LAppWavFileHandler();
    public _debugMode: boolean = LAppDefine.DebugLogEnable;

    // 添加必要的ID参数
    public _idParamAngleX: CubismIdHandle = CubismFramework.getIdManager().getId(CubismDefaultParameterId.ParamAngleX);
    public _idParamAngleY: CubismIdHandle = CubismFramework.getIdManager().getId(CubismDefaultParameterId.ParamAngleY);
    public _idParamAngleZ: CubismIdHandle = CubismFramework.getIdManager().getId(CubismDefaultParameterId.ParamAngleZ);
    public _idParamBodyAngleX: CubismIdHandle = CubismFramework.getIdManager().getId(CubismDefaultParameterId.ParamBodyAngleX);

    /**
     * 构造函数
     */
    constructor() {
        super();
        // 初始化其他必要的组件
    }
}
