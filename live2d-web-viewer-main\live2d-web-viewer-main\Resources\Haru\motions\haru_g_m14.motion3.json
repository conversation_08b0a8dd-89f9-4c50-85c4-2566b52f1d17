{"Version": 3, "Meta": {"Duration": 3.03, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 284, "TotalPointCount": 749, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 1, 1, 2, 1, 3, 1, 0, 3.03, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, 1, 0.767, 1, 1, 0.889, 1, 1.011, 1.06, 1.133, 0.632, 1, 1.256, 0.204, 1.378, -2, 1.5, -2, 1, 2, -2, 2.5, -2, 3, -2, 0, 3.033, -2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.433, 0, 0.5, -1, 0.567, -1, 1, 0.689, -1, 0.811, 16, 0.933, 16, 1, 1.067, 16, 1.2, 16.106, 1.333, 13.915, 1, 1.444, 12.09, 1.556, -1, 1.667, -1, 1, 2.111, -1, 2.556, -1, 3, -1, 0, 3.033, -1]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.767, 0, 0.9, -9, 1.033, -9, 1, 1.1, -9, 1.167, -9.696, 1.233, -5.567, 1, 1.356, 2.004, 1.478, 15.49, 1.6, 19, 1, 1.778, 19.291, 1.956, 18, 2.133, 18, 1, 2.422, 18, 2.711, 18, 3, 18, 0, 3.033, 18]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 1, 1, 0, 2, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.589, 1, 0.678, 0, 0.767, 0, 1, 0.8, 0, 0.833, 0, 0.867, 0, 1, 0.956, 0, 1.044, 1.332, 1.133, 1.616, 1, 1.256, 2.007, 1.378, 2, 1.5, 2, 1, 1.667, 2, 1.833, 2, 2, 2, 1, 2.044, 2, 2.089, 0, 2.133, 0, 1, 2.167, 0, 2.2, 0, 2.233, 0, 1, 2.311, 0, 2.389, 2, 2.467, 2, 1, 2.644, 2, 2.822, 2, 3, 2, 0, 3.033, 2]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.589, 1, 0.678, 0, 0.767, 0, 1, 0.8, 0, 0.833, 0, 0.867, 0, 1, 0.956, 0, 1.044, 1.332, 1.133, 1.616, 1, 1.256, 2.007, 1.378, 2, 1.5, 2, 1, 1.667, 2, 1.833, 2, 2, 2, 1, 2.044, 2, 2.089, 0, 2.133, 0, 1, 2.167, 0, 2.2, 0, 2.233, 0, 1, 2.311, 0, 2.389, 2, 2.467, 2, 1, 2.644, 2, 2.822, 2, 3, 2, 0, 3.033, 2]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0.3, 1.5, 0.3, 1, 2, 0.3, 2.5, 0.3, 3, 0.3, 0, 3.033, 0.3]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.039, 1.133, 0.123, 1, 1.256, 0.172, 1.378, 0.2, 1.5, 0.2, 1, 1.667, 0.2, 1.833, 0.2, 2, 0.2, 1, 2.044, 0.2, 2.089, -0.2, 2.133, -0.2, 1, 2.167, -0.2, 2.2, -0.2, 2.233, -0.2, 1, 2.311, -0.2, 2.389, 0.2, 2.467, 0.2, 1, 2.644, 0.2, 2.822, 0.2, 3, 0.2, 0, 3.033, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, -0.1, 0.767, -0.1, 1, 0.8, -0.1, 0.833, -0.1, 0.867, -0.1, 1, 0.956, -0.1, 1.044, 0.291, 1.133, 0.493, 1, 1.256, 0.77, 1.378, 0.8, 1.5, 0.8, 1, 2, 0.8, 2.5, 0.8, 3, 0.8, 0, 3.033, 0.8]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, -0.1, 0.767, -0.1, 1, 0.8, -0.1, 0.833, -0.1, 0.867, -0.1, 1, 0.956, -0.1, 1.044, 0.291, 1.133, 0.493, 1, 1.256, 0.77, 1.378, 0.8, 1.5, 0.8, 1, 2, 0.8, 2.5, 0.8, 3, 0.8, 0, 3.033, 0.8]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.039, 1.133, 0.123, 1, 1.256, 0.172, 1.378, 0.2, 1.5, 0.2, 1, 2, 0.2, 2.5, 0.2, 3, 0.2, 0, 3.033, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.039, 1.133, 0.123, 1, 1.256, 0.172, 1.378, 0.2, 1.5, 0.2, 1, 2, 0.2, 2.5, 0.2, 3, 0.2, 0, 3.033, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.039, 1.133, 0.123, 1, 1.256, 0.172, 1.378, 0.2, 1.5, 0.2, 1, 2, 0.2, 2.5, 0.2, 3, 0.2, 0, 3.033, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.039, 1.133, 0.123, 1, 1.256, 0.172, 1.378, 0.2, 1.5, 0.2, 1, 2, 0.2, 2.5, 0.2, 3, 0.2, 0, 3.033, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.078, 1.133, 0.246, 1, 1.256, 0.344, 1.378, 0.4, 1.5, 0.4, 1, 2, 0.4, 2.5, 0.4, 3, 0.4, 0, 3.033, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0.078, 1.133, 0.246, 1, 1.256, 0.344, 1.378, 0.4, 1.5, 0.4, 1, 2, 0.4, 2.5, 0.4, 3, 0.4, 0, 3.033, 0.4]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.589, 1, 0.678, -0.5, 0.767, -0.5, 1, 0.889, -0.5, 1.011, -0.232, 1.133, -0.232, 1, 1.256, -0.232, 1.378, -1, 1.5, -1, 1, 2, -1, 2.5, -1, 3, -1, 0, 3.033, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, 3, 0.767, 3, 1, 0.889, 3, 1.011, 1.294, 1.133, -0.616, 1, 1.256, -2.526, 1.378, -3, 1.5, -3, 1, 2, -3, 2.5, -3, 3, -3, 0, 3.033, -3]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 7, 1.133, 7, 1, 1.256, 7, 1.378, -9, 1.5, -9, 1, 2, -9, 2.5, -9, 3, -9, 0, 3.033, -9]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, -4.509, 0.767, -4.728, 1, 0.889, -5.029, 1.011, -5, 1.133, -5, 1, 1.256, -5, 1.378, 7, 1.5, 7, 1, 2, 7, 2.5, 7, 3, 7, 0, 3.033, 7]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, -1.507, 0.767, -1.507, 1, 0.889, -1.507, 1.011, -1.675, 1.133, -0.529, 1, 1.256, 0.618, 1.378, 10, 1.5, 10, 1, 2, 10, 2.5, 10, 3, 10, 0, 3.033, 10]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.711, 1, 0.922, 0.3, 1.133, 0.3, 1, 1.256, 0.3, 1.378, 0.327, 1.5, 0.4, 1, 1.6, 0.459, 1.7, 0.5, 1.8, 0.5, 1, 2.2, 0.5, 2.6, 0.5, 3, 0.5, 0, 3.033, 0.5]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.711, 1, 0.922, 0.3, 1.133, 0.3, 1, 1.256, 0.3, 1.378, 0.327, 1.5, 0.4, 1, 1.6, 0.459, 1.7, 0.5, 1.8, 0.5, 1, 2.2, 0.5, 2.6, 0.5, 3, 0.5, 0, 3.033, 0.5]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.467, 0, 1.7, 0, 1.933, 0, 1, 2.289, 0, 2.644, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, 0.9, 0.767, 0.9, 1, 0.922, 0.9, 1.078, 0.681, 1.233, 0, 1, 1.344, -0.486, 1.456, -0.9, 1.567, -0.9, 1, 1.689, -0.9, 1.811, -0.6, 1.933, -0.6, 1, 2.289, -0.6, 2.644, -0.6, 3, -0.6, 0, 3.033, -0.6]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0, 1.233, 0, 1, 1.467, 0, 1.7, 0, 1.933, 0, 1, 2.289, 0, 2.644, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.589, 0, 0.678, -0.9, 0.767, -0.9, 1, 0.922, -0.9, 1.078, -0.681, 1.233, 0, 1, 1.344, 0.486, 1.456, 0.9, 1.567, 0.9, 1, 1.689, 0.9, 1.811, 0.6, 1.933, 0.6, 1, 2.289, 0.6, 2.644, 0.6, 3, 0.6, 0, 3.033, 0.6]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.711, 0, 0.922, 0, 1.133, 0, 1, 1.256, 0, 1.378, 0, 1.5, 0, 1, 2, 0, 2.5, 0, 3, 0, 0, 3.033, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 0, 2, 3, 0, 0, 3.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 0, 2, 3, 0, 0, 3.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 3, 1, 0, 3.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 3, 0, 0, 3.03, 0]}]}