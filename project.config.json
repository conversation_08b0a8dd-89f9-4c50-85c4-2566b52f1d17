{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript", "less"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "minified": false, "minifyWXSS": false, "minifyWXML": false, "ignoreUploadUnusedFiles": false, "uglifyFileName": true, "condition": false, "coverView": false, "postcss": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "localPlugins": false, "disableUseStrict": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "packOptions": {"ignore": [], "include": []}, "appid": "wx9a78ca18b5d9e595", "libVersion": "3.1.1"}