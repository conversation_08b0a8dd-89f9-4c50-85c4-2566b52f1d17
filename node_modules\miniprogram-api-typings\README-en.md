# Wechat Mini Program API Typings

> [中文版本](./README.md)

[![Published on NPM](https://img.shields.io/npm/v/miniprogram-api-typings.svg?style=flat)](https://www.npmjs.com/package/miniprogram-api-typings)
[![MIT License](https://img.shields.io/github/license/wechat-miniprogram/api-typings.svg)](https://github.com/wechat-miniprogram/api-typings)
[![Travis CI Test Status](https://travis-ci.org/wechat-miniprogram/api-typings.svg?branch=master)](https://travis-ci.org/wechat-miniprogram/api-typings)

Type definitions for APIs of Wechat Mini Program in TypeScript

## Install

Install by NPM:
```bash
# install definitions for latest base library
npm install miniprogram-api-typings
```

or specify a base library version:

```bash
# install definitions for base library version 2.4.1
npm install miniprogram-api-typings@2.4.1
```

## Versions

Check out all available versions corresponding to base library version in [VERSIONS.md](https://github.com/wechat-miniprogram/api-typings/blob/master/VERSIONS.md)

## Changelog

See [CHANGELOG.md](https://github.com/wechat-miniprogram/api-typings/blob/master/CHANGELOG.md) (Chinese only)

## Contribution

Definitions of Wechat APIs (`lib.wx.api.d.ts`) are auto-generated together with our [documentations](https://developers.weixin.qq.com/miniprogram/dev/index.html), therefore PRs including that file will __not__ be merged. If you found some APIs defined wrongly, create an issue instead.

Both PR and issue are welcomed for definitions of pages (`Page`), custom components (`Component`) and other else, since they are written manually. Help us improve this definition if you have any bug reports or suggestions! Thanks for contributing!

### Contributors

- [Baran](https://github.com/baranwang)
- [MinLiang Zeng](https://github.com/zenml/)

### Automated tests

We use [`tsd`](https://github.com/SamVerschueren/tsd) to check if this definition is working properly. All test cases are under folder `test`.

To perform an automated test, clone this repo, `npm install --save-dev` and `npm test`.

If you have test case that fails the test, an issue or PR will be great. Strong test case that passes are also welcomed.
